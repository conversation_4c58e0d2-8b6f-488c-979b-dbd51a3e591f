<mvc:View controllerName="reconciliation.controller.MasterData"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns="sap.m"
    xmlns:f="sap.f"
    xmlns:core="sap.ui.core">
    <Page id="masterDataPage" title="{i18n>masterDataTitle}" showNavButton="true" navButtonPress="onNavBack">
        <content>
            <ScrollContainer id="masterDataScrollContainer" height="100%" width="100%" horizontal="false" vertical="true">
                
                <!-- Master Data Header -->
                <VBox id="masterDataHeaderSection" class="sapUiMediumMargin">
                    <Title id="masterDataHeaderTitle" text="Master Data Management" level="H2" class="sapUiMediumMarginBottom"/>
                    <MessageStrip id="masterDataInfoStrip" 
                        text="Manage plants, products, and vendors master data used in demand-supply reconciliation analysis."
                        type="Information"
                        class="sapUiMediumMarginBottom"/>
                </VBox>

                <!-- Entity Selection -->
                <VBox id="entitySelectionSection" class="sapUiMediumMargin">
                    <Panel id="entitySelectionPanel" headerText="Select Master Data Entity" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="entitySelectionBar" class="sapUiMediumMargin">
                                <SegmentedButton id="entitySelector" 
                                    selectedKey="{masterDataModel>/selectedEntity}"
                                    selectionChange="onEntitySelectionChange"
                                    class="sapUiTinyMarginEnd">
                                    <items>
                                        <SegmentedButtonItem id="plantsSegment" key="plants" text="Plants" icon="sap-icon://factory"/>
                                        <SegmentedButtonItem id="productsSegment" key="products" text="Products" icon="sap-icon://product"/>
                                        <SegmentedButtonItem id="vendorsSegment" key="vendors" text="Vendors" icon="sap-icon://supplier"/>
                                    </items>
                                </SegmentedButton>
                                <Button id="createNewButton" 
                                    text="Create New" 
                                    icon="sap-icon://add"
                                    type="Emphasized" 
                                    press="onCreateNew"
                                    class="sapUiTinyMarginStart"/>
                            </HBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Search and Filter Section -->
                <VBox id="searchFilterSection" class="sapUiMediumMargin">
                    <Panel id="searchFilterPanel" headerText="Search & Filter" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="searchFilterBar" class="sapUiMediumMargin">
                                <SearchField id="searchField" 
                                    placeholder="Search by name, code, or description..."
                                    value="{masterDataModel>/searchQuery}"
                                    search="onSearch"
                                    width="300px"
                                    class="sapUiTinyMarginEnd"/>
                                <ComboBox id="masterDataStatusFilter"
                                    placeholder="Filter by Status"
                                    selectedKey="{masterDataModel>/filters/status}"
                                    selectionChange="onFilterChange"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item id="statusFilterAllItem" key="" text="All Status"/>
                                    <core:Item id="statusFilterActiveItem" key="ACTIVE" text="Active"/>
                                    <core:Item id="statusFilterInactiveItem" key="INACTIVE" text="Inactive"/>
                                </ComboBox>
                                <ComboBox id="categoryFilter" 
                                    placeholder="Filter by Category"
                                    selectedKey="{masterDataModel>/filters/category}"
                                    selectionChange="onFilterChange"
                                    visible="{= ${masterDataModel>/selectedEntity} === 'products'}"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item id="categoryFilterAllItem" key="" text="All Categories"/>
                                    <core:Item id="categoryFilterRawItem" key="RAW_MATERIALS" text="Raw Materials"/>
                                    <core:Item id="categoryFilterFinishedItem" key="FINISHED_GOODS" text="Finished Goods"/>
                                    <core:Item id="categoryFilterSemiItem" key="SEMI_FINISHED" text="Semi-Finished"/>
                                    <core:Item id="categoryFilterConsumablesItem" key="CONSUMABLES" text="Consumables"/>
                                </ComboBox>
                                <ComboBox id="regionFilter" 
                                    placeholder="Filter by Region"
                                    selectedKey="{masterDataModel>/filters/region}"
                                    selectionChange="onFilterChange"
                                    visible="{= ${masterDataModel>/selectedEntity} === 'plants' || ${masterDataModel>/selectedEntity} === 'vendors'}"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item id="regionFilterAllItem" key="" text="All Regions"/>
                                    <core:Item id="regionFilterNorthAmericaItem" key="NORTH_AMERICA" text="North America"/>
                                    <core:Item id="regionFilterEuropeItem" key="EUROPE" text="Europe"/>
                                    <core:Item id="regionFilterAsiaPacificItem" key="ASIA_PACIFIC" text="Asia Pacific"/>
                                    <core:Item id="regionFilterLatinAmericaItem" key="LATIN_AMERICA" text="Latin America"/>
                                </ComboBox>
                                <Button id="masterDataClearFiltersButton"
                                    text="{i18n>clearFilters}"
                                    icon="sap-icon://clear-filter"
                                    type="Transparent"
                                    press="onClearFilters"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="refreshDataButton" 
                                    text="{i18n>refresh}" 
                                    icon="sap-icon://refresh"
                                    type="Default" 
                                    press="onRefreshData"/>
                            </HBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Bulk Actions Section -->
                <VBox id="masterDataBulkActionsSection" class="sapUiMediumMargin">
                    <Panel id="masterDataBulkActionsPanel" headerText="Bulk Actions" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="masterDataBulkActionsBar" class="sapUiMediumMargin">
                                <Button id="bulkActivateButton" 
                                    text="Activate Selected" 
                                    icon="sap-icon://activate"
                                    type="Accept" 
                                    press="onBulkActivate"
                                    enabled="{masterDataModel>/hasSelectedItems}"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="bulkDeactivateButton" 
                                    text="Deactivate Selected" 
                                    icon="sap-icon://deactivate"
                                    type="Reject" 
                                    press="onBulkDeactivate"
                                    enabled="{masterDataModel>/hasSelectedItems}"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="bulkDeleteButton" 
                                    text="Delete Selected" 
                                    icon="sap-icon://delete"
                                    type="Transparent" 
                                    press="onBulkDelete"
                                    enabled="{masterDataModel>/hasSelectedItems}"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="importDataButton" 
                                    text="Import Data" 
                                    icon="sap-icon://upload"
                                    type="Default" 
                                    press="onImportData"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="exportDataButton" 
                                    text="Export Data" 
                                    icon="sap-icon://download"
                                    type="Default" 
                                    press="onExportData"/>
                            </HBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Data Table -->
                <VBox id="dataTableSection" class="sapUiMediumMargin">
                    <Table id="masterDataTable"
                        items="{masterDataModel>/currentData}"
                        mode="MultiSelect"
                        selectionChange="onSelectionChange"
                        class="sapUiResponsiveMargin"
                        growing="true"
                        growingThreshold="100">
                        <headerToolbar>
                            <Toolbar id="masterDataToolbar">
                                <Title id="masterDataTableTitle" text="{masterDataModel>/tableTitle} ({masterDataModel>/currentData/length})"/>
                                <ToolbarSpacer id="masterDataToolbarSpacer"/>
                                <Button id="exportTableButton" 
                                    text="{i18n>export}" 
                                    icon="sap-icon://excel-attachment"
                                    type="Transparent" 
                                    press="onExportData"/>
                            </Toolbar>
                        </headerToolbar>
                        
                        <!-- Plants Columns -->
                        <columns>
                            <Column id="codeColumn">
                                <Text id="codeColumnHeader" text="{masterDataModel>/columnHeaders/code}"/>
                            </Column>
                            <Column id="nameColumn">
                                <Text id="nameColumnHeader" text="{masterDataModel>/columnHeaders/name}"/>
                            </Column>
                            <Column id="masterDataDescriptionColumn">
                                <Text id="masterDataDescriptionColumnHeader" text="Description"/>
                            </Column>
                            <Column id="locationColumn">
                                <Text id="locationColumnHeader" text="{masterDataModel>/columnHeaders/location}"/>
                            </Column>
                            <Column id="masterDataStatusColumn">
                                <Text id="masterDataStatusColumnHeader" text="Status"/>
                            </Column>
                            <Column id="masterDataCreatedAtColumn">
                                <Text id="masterDataCreatedAtColumnHeader" text="Created"/>
                            </Column>
                            <Column id="masterDataActionsColumn">
                                <Text id="masterDataActionsColumnHeader" text="Actions"/>
                            </Column>
                        </columns>
                        
                        <items>
                            <ColumnListItem id="masterDataListItem" press="onItemPress">
                                <Link id="codeLink" text="{masterDataModel>plantCode}{masterDataModel>productCode}{masterDataModel>vendorCode}" press="onItemPress"/>
                                <Text id="nameText" text="{masterDataModel>plantName}{masterDataModel>productName}{masterDataModel>vendorName}"/>
                                <Text id="masterDataDescriptionText" text="{masterDataModel>description}" maxLines="2"/>
                                <Text id="masterDataLocationText" text="{masterDataModel>location}{masterDataModel>category}{masterDataModel>contactPerson}"/>
                                <ObjectStatus id="masterDataStatusObject"
                                    text="{masterDataModel>status}"
                                    state="{path: 'masterDataModel>status', formatter: '.formatStatusState'}"/>
                                <Text id="masterDataCreatedAtText" text="{path: 'masterDataModel>createdAt', formatter: '.formatDate'}"/>
                                <HBox id="masterDataActionButtons">
                                    <Button id="masterDataViewDetailsButton"
                                        icon="sap-icon://detail-view"
                                        type="Transparent"
                                        press="onViewDetails"
                                        tooltip="View Details"/>
                                    <Button id="editButton" 
                                        icon="sap-icon://edit" 
                                        type="Transparent" 
                                        press="onEdit"
                                        tooltip="Edit"/>
                                    <Button id="duplicateButton" 
                                        icon="sap-icon://duplicate" 
                                        type="Transparent" 
                                        press="onDuplicate"
                                        tooltip="Duplicate"/>
                                    <Button id="deleteButton" 
                                        icon="sap-icon://delete" 
                                        type="Transparent" 
                                        press="onDelete"
                                        tooltip="Delete"/>
                                </HBox>
                            </ColumnListItem>
                        </items>
                    </Table>
                </VBox>

                <!-- Statistics Section -->
                <VBox id="statisticsSection" class="sapUiMediumMargin">
                    <Title id="statisticsTitle" text="Statistics" level="H3" class="sapUiMediumMarginBottom"/>
                    <Panel id="statisticsPanel" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="statisticsContent" class="sapUiMediumMargin">
                                <VBox id="countStats" class="sapUiTinyMargin">
                                    <Title id="countStatsTitle" text="Count Statistics" level="H4" class="sapUiTinyMarginBottom"/>
                                    <VBox id="countStatsContent">
                                        <HBox id="totalCountBox" class="sapUiTinyMarginBottom">
                                            <Text id="totalCountLabel" text="Total Records: " class="sapUiTinyMarginEnd"/>
                                            <Text id="totalCountValue" text="{masterDataModel>/statistics/total}"/>
                                        </HBox>
                                        <HBox id="activeCountBox" class="sapUiTinyMarginBottom">
                                            <Text id="activeCountLabel" text="Active: " class="sapUiTinyMarginEnd"/>
                                            <Text id="activeCountValue" text="{masterDataModel>/statistics/active}"/>
                                        </HBox>
                                        <HBox id="inactiveCountBox" class="sapUiTinyMarginBottom">
                                            <Text id="inactiveCountLabel" text="Inactive: " class="sapUiTinyMarginEnd"/>
                                            <Text id="inactiveCountValue" text="{masterDataModel>/statistics/inactive}"/>
                                        </HBox>
                                        <HBox id="recentlyAddedBox" class="sapUiTinyMarginBottom">
                                            <Text id="recentlyAddedLabel" text="Added This Month: " class="sapUiTinyMarginEnd"/>
                                            <Text id="recentlyAddedValue" text="{masterDataModel>/statistics/recentlyAdded}"/>
                                        </HBox>
                                    </VBox>
                                </VBox>
                                <VBox id="categoryStats" class="sapUiTinyMargin" visible="{= ${masterDataModel>/selectedEntity} === 'products'}">
                                    <Title id="categoryStatsTitle" text="Category Breakdown" level="H4" class="sapUiTinyMarginBottom"/>
                                    <VBox id="categoryStatsContent">
                                        <HBox id="rawMaterialsBox" class="sapUiTinyMarginBottom">
                                            <Text id="rawMaterialsLabel" text="Raw Materials: " class="sapUiTinyMarginEnd"/>
                                            <Text id="rawMaterialsValue" text="{masterDataModel>/statistics/categories/rawMaterials}"/>
                                        </HBox>
                                        <HBox id="finishedGoodsBox" class="sapUiTinyMarginBottom">
                                            <Text id="finishedGoodsLabel" text="Finished Goods: " class="sapUiTinyMarginEnd"/>
                                            <Text id="finishedGoodsValue" text="{masterDataModel>/statistics/categories/finishedGoods}"/>
                                        </HBox>
                                        <HBox id="semiFinishedBox" class="sapUiTinyMarginBottom">
                                            <Text id="semiFinishedLabel" text="Semi-Finished: " class="sapUiTinyMarginEnd"/>
                                            <Text id="semiFinishedValue" text="{masterDataModel>/statistics/categories/semiFinished}"/>
                                        </HBox>
                                    </VBox>
                                </VBox>
                                <VBox id="regionStats" class="sapUiTinyMargin" visible="{= ${masterDataModel>/selectedEntity} === 'plants' || ${masterDataModel>/selectedEntity} === 'vendors'}">
                                    <Title id="regionStatsTitle" text="Regional Distribution" level="H4" class="sapUiTinyMarginBottom"/>
                                    <VBox id="regionStatsContent">
                                        <HBox id="northAmericaBox" class="sapUiTinyMarginBottom">
                                            <Text id="northAmericaLabel" text="North America: " class="sapUiTinyMarginEnd"/>
                                            <Text id="northAmericaValue" text="{masterDataModel>/statistics/regions/northAmerica}"/>
                                        </HBox>
                                        <HBox id="europeBox" class="sapUiTinyMarginBottom">
                                            <Text id="europeLabel" text="Europe: " class="sapUiTinyMarginEnd"/>
                                            <Text id="europeValue" text="{masterDataModel>/statistics/regions/europe}"/>
                                        </HBox>
                                        <HBox id="asiaPacificBox" class="sapUiTinyMarginBottom">
                                            <Text id="asiaPacificLabel" text="Asia Pacific: " class="sapUiTinyMarginEnd"/>
                                            <Text id="asiaPacificValue" text="{masterDataModel>/statistics/regions/asiaPacific}"/>
                                        </HBox>
                                    </VBox>
                                </VBox>
                            </HBox>
                        </content>
                    </Panel>
                </VBox>
            </ScrollContainer>
        </content>
    </Page>
</mvc:View>
