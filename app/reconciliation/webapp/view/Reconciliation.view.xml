<mvc:View controllerName="reconciliation.controller.Reconciliation"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns="sap.m"
    xmlns:f="sap.f"
    xmlns:core="sap.ui.core">
    <Page id="reconciliationPage" title="{i18n>reconciliationTitle}" showNavButton="true" navButtonPress="onNavBack">
        <content>
            <ScrollContainer id="reconciliationScrollContainer" height="100%" width="100%" horizontal="false" vertical="true">
                
                <!-- Analysis Parameters Section -->
                <VBox id="parametersSection" class="sapUiMediumMargin">
                    <Title id="parametersTitle" text="Analysis Parameters" level="H2" class="sapUiMediumMarginBottom"/>
                    
                    <Panel id="parametersPanel" headerText="Select Analysis Scope" class="sapUiResponsiveMargin">
                        <content>
                            <f:SimpleForm id="parametersForm"
                                editable="true"
                                layout="ResponsiveGridLayout"
                                labelSpanXL="3"
                                labelSpanL="3"
                                labelSpanM="3"
                                labelSpanS="12"
                                adjustLabelSpan="false"
                                emptySpanXL="4"
                                emptySpanL="4"
                                emptySpanM="4"
                                emptySpanS="0"
                                columnsXL="2"
                                columnsL="2"
                                columnsM="1"
                                singleContainerFullSize="false"
                                class="sapUiMediumMargin">
                                <f:content>
                                    <Label id="analysisDateLabel" text="Analysis Date" required="true"/>
                                    <DatePicker id="analysisDatePicker" 
                                        value="{reconciliationModel>/parameters/analysisDate}" 
                                        valueState="{reconciliationModel>/parameters/analysisDateState}"/>
                                    
                                    <Label id="plantSelectionLabel" text="Plant Selection"/>
                                    <MultiComboBox id="plantMultiCombo" 
                                        selectedKeys="{reconciliationModel>/parameters/selectedPlants}"
                                        items="{/Plants}"
                                        selectionChange="onPlantSelectionChange">
                                        <core:Item id="_IDGenItem15" key="{ID}" text="{plantName} ({plantCode})"/>
                                    </MultiComboBox>
                                    
                                    <Label id="productCategoryLabel" text="Product Category"/>
                                    <MultiComboBox id="productCategoryMultiCombo" 
                                        selectedKeys="{reconciliationModel>/parameters/selectedCategories}"
                                        items="{/ProductCategories}"
                                        selectionChange="onCategorySelectionChange">
                                        <core:Item id="_IDGenItem16" key="{ID}" text="{categoryName}"/>
                                    </MultiComboBox>
                                    
                                    <Label id="analysisTypeLabel" text="Analysis Type"/>
                                    <ComboBox id="analysisTypeCombo" 
                                        selectedKey="{reconciliationModel>/parameters/analysisType}">
                                        <core:Item id="_IDGenItem17" key="FULL" text="Full Analysis"/>
                                        <core:Item id="_IDGenItem18" key="VARIANCE_ONLY" text="Variance Analysis Only"/>
                                        <core:Item id="_IDGenItem19" key="SHORTAGE_FOCUS" text="Shortage Focus"/>
                                        <core:Item id="_IDGenItem20" key="SURPLUS_FOCUS" text="Surplus Focus"/>
                                    </ComboBox>
                                    
                                    <Label id="toleranceLabel" text="Variance Tolerance (%)"/>
                                    <Input id="toleranceInput" 
                                        value="{reconciliationModel>/parameters/varianceTolerance}" 
                                        type="Number"
                                        description="Variances below this threshold will be ignored"/>
                                    
                                    <Label id="aiRecommendationsLabel" text="Generate AI Recommendations"/>
                                    <CheckBox id="aiRecommendationsCheckBox" 
                                        selected="{reconciliationModel>/parameters/generateRecommendations}"/>
                                </f:content>
                            </f:SimpleForm>
                        </content>
                    </Panel>
                    
                    <!-- Action Buttons -->
                    <HBox id="analysisActions" class="sapUiMediumMargin">
                        <Button id="runAnalysisButton" 
                            text="{i18n>runReconciliation}" 
                            icon="sap-icon://process"
                            type="Emphasized" 
                            press="onRunAnalysis"
                            enabled="{reconciliationModel>/parametersValid}"
                            class="sapUiTinyMarginEnd"/>
                        <Button id="clearParametersButton" 
                            text="{i18n>clear}" 
                            icon="sap-icon://clear-all"
                            type="Default" 
                            press="onClearParameters"
                            class="sapUiTinyMarginEnd"/>
                        <Button id="saveParametersButton" 
                            text="{i18n>saveParameters}" 
                            icon="sap-icon://save"
                            type="Transparent" 
                            press="onSaveParameters"/>
                    </HBox>
                </VBox>

                <!-- Analysis Progress Section -->
                <VBox id="progressSection" 
                    visible="{reconciliationModel>/analysisRunning}"
                    class="sapUiMediumMargin">
                    <Title id="progressTitle" text="Analysis Progress" level="H3" class="sapUiMediumMarginBottom"/>
                    <Panel id="progressPanel" class="sapUiResponsiveMargin">
                        <content>
                            <VBox id="progressContent" class="sapUiMediumMargin">
                                <ProgressIndicator id="analysisProgress" 
                                    percentValue="{reconciliationModel>/analysisProgress}" 
                                    displayValue="{reconciliationModel>/progressText}"
                                    state="Information"
                                    class="sapUiMediumMarginBottom"/>
                                <Text id="progressStatusText" text="{reconciliationModel>/progressStatus}"/>
                                <Button id="cancelAnalysisButton" 
                                    text="{i18n>cancel}" 
                                    icon="sap-icon://stop"
                                    type="Reject" 
                                    press="onCancelAnalysis"
                                    class="sapUiMediumMarginTop"/>
                            </VBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Analysis Results Section -->
                <VBox id="resultsSection" 
                    visible="{reconciliationModel>/showResults}"
                    class="sapUiMediumMargin">
                    <Title id="resultsTitle" text="Reconciliation Results" level="H2" class="sapUiMediumMarginBottom"/>
                    
                    <!-- Summary Cards -->
                    <HBox id="summaryCards" class="sapUiMediumMarginBottom">
                        <VBox id="totalVarianceCard" class="sapUiTinyMargin">
                            <NumericTile id="totalVarianceTile"
                                header="Total Variance"
                                subheader="All Products"
                                value="{reconciliationModel>/results/summary/totalVariance}"
                                valueColor="{path: 'reconciliationModel>/results/summary/totalVariance', formatter: '.formatVarianceColor'}"
                                indicator="{path: 'reconciliationModel>/results/summary/varianceTrend', formatter: '.formatTrendIndicator'}"
                                size="M"
                                press="onSummaryTilePress"/>
                        </VBox>
                        <VBox id="shortageItemsCard" class="sapUiTinyMargin">
                            <NumericTile id="shortageItemsTile"
                                header="Shortage Items"
                                subheader="Requiring Action"
                                value="{reconciliationModel>/results/summary/shortageItems}"
                                valueColor="Error"
                                size="M"
                                press="onSummaryTilePress"/>
                        </VBox>
                        <VBox id="surplusItemsCard" class="sapUiTinyMargin">
                            <NumericTile id="surplusItemsTile"
                                header="Surplus Items"
                                subheader="Available for Reallocation"
                                value="{reconciliationModel>/results/summary/surplusItems}"
                                valueColor="Good"
                                size="M"
                                press="onSummaryTilePress"/>
                        </VBox>
                        <VBox id="balancedItemsCard" class="sapUiTinyMargin">
                            <NumericTile id="balancedItemsTile"
                                header="Balanced Items"
                                subheader="Within Tolerance"
                                value="{reconciliationModel>/results/summary/balancedItems}"
                                valueColor="Neutral"
                                size="M"
                                press="onSummaryTilePress"/>
                        </VBox>
                    </HBox>

                    <!-- Results Filter Bar -->
                    <Panel id="filterPanel" headerText="Filter Results" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="filterBar" class="sapUiMediumMargin">
                                <ComboBox id="statusFilter" 
                                    placeholder="Filter by Status"
                                    selectedKey="{reconciliationModel>/filters/status}"
                                    selectionChange="onFilterChange"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item id="_IDGenItem21" key="" text="All Status"/>
                                    <core:Item id="_IDGenItem22" key="SHORTAGE" text="Shortage"/>
                                    <core:Item id="_IDGenItem23" key="SURPLUS" text="Surplus"/>
                                    <core:Item id="_IDGenItem24" key="BALANCED" text="Balanced"/>
                                </ComboBox>
                                <ComboBox id="plantFilter" 
                                    placeholder="Filter by Plant"
                                    selectedKey="{reconciliationModel>/filters/plant}"
                                    selectionChange="onFilterChange"
                                    items="{/Plants}"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item id="_IDGenItem25" key="" text="All Plants"/>
                                    <core:Item id="_IDGenItem26" key="{ID}" text="{plantName}"/>
                                </ComboBox>
                                <SearchField id="productSearch" 
                                    placeholder="Search Products"
                                    value="{reconciliationModel>/filters/productSearch}"
                                    search="onProductSearch"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="clearFiltersButton" 
                                    text="{i18n>clearFilters}" 
                                    icon="sap-icon://clear-filter"
                                    type="Transparent" 
                                    press="onClearFilters"/>
                            </HBox>
                        </content>
                    </Panel>

                    <!-- Results Table -->
                    <Table id="resultsTable"
                        items="{path: 'reconciliationModel>/results/items', sorter: {path: 'variance', descending: true}}"
                        mode="MultiSelect"
                        class="sapUiResponsiveMargin"
                        growing="true"
                        growingThreshold="50">
                        <headerToolbar>
                            <Toolbar id="resultsToolbar">
                                <Title id="resultsTableTitle" text="Reconciliation Items ({reconciliationModel>/results/items/length})"/>
                                <ToolbarSpacer id="resultsToolbarSpacer"/>
                                <Button id="exportResultsButton" 
                                    text="{i18n>export}" 
                                    icon="sap-icon://excel-attachment"
                                    type="Transparent" 
                                    press="onExportResults"/>
                                <Button id="generateRecommendationsButton" 
                                    text="{i18n>generateRecommendations}" 
                                    icon="sap-icon://ai"
                                    type="Default" 
                                    press="onGenerateRecommendations"
                                    enabled="{= ${reconciliationModel>/results/items}.length > 0}"/>
                            </Toolbar>
                        </headerToolbar>
                        <columns>
                            <Column id="plantColumn" sortProperty="plant/plantName">
                                <Text id="plantColumnHeader" text="Plant"/>
                            </Column>
                            <Column id="productColumn" sortProperty="product/productName">
                                <Text id="productColumnHeader" text="Product"/>
                            </Column>
                            <Column id="demandColumn" sortProperty="demandQty">
                                <Text id="demandColumnHeader" text="Demand"/>
                            </Column>
                            <Column id="supplyColumn" sortProperty="supplyQty">
                                <Text id="supplyColumnHeader" text="Supply"/>
                            </Column>
                            <Column id="stockColumn" sortProperty="stockQty">
                                <Text id="stockColumnHeader" text="Stock"/>
                            </Column>
                            <Column id="varianceColumn" sortProperty="variance">
                                <Text id="varianceColumnHeader" text="Variance"/>
                            </Column>
                            <Column id="statusColumn" sortProperty="status">
                                <Text id="statusColumnHeader" text="Status"/>
                            </Column>
                            <Column id="actionsColumn">
                                <Text id="actionsColumnHeader" text="Actions"/>
                            </Column>
                        </columns>
                        <items>
                            <ColumnListItem id="resultListItem" press="onResultItemPress">
                                <Text id="plantText" text="{reconciliationModel>plant/plantName}"/>
                                <Text id="productText" text="{reconciliationModel>product/productName}"/>
                                <ObjectNumber id="demandNumber" 
                                    number="{reconciliationModel>demandQty}" 
                                    unit="{reconciliationModel>product/unitOfMeasure}"/>
                                <ObjectNumber id="supplyNumber" 
                                    number="{reconciliationModel>supplyQty}" 
                                    unit="{reconciliationModel>product/unitOfMeasure}"/>
                                <ObjectNumber id="stockNumber" 
                                    number="{reconciliationModel>stockQty}" 
                                    unit="{reconciliationModel>product/unitOfMeasure}"/>
                                <ObjectNumber id="varianceNumber" 
                                    number="{reconciliationModel>variance}" 
                                    unit="{reconciliationModel>product/unitOfMeasure}"
                                    state="{path: 'reconciliationModel>variance', formatter: '.formatVarianceState'}"/>
                                <ObjectStatus id="statusObject" 
                                    text="{reconciliationModel>status}" 
                                    state="{path: 'reconciliationModel>status', formatter: '.formatStatusState'}"/>
                                <HBox id="actionButtons">
                                    <Button id="detailsButton" 
                                        icon="sap-icon://detail-view" 
                                        type="Transparent" 
                                        press="onViewDetails"
                                        tooltip="View Details"/>
                                    <Button id="recommendButton" 
                                        icon="sap-icon://ai" 
                                        type="Transparent" 
                                        press="onGetRecommendation"
                                        tooltip="Get AI Recommendation"
                                        visible="{= ${reconciliationModel>status} !== 'BALANCED'}"/>
                                </HBox>
                            </ColumnListItem>
                        </items>
                    </Table>
                </VBox>
            </ScrollContainer>
        </content>
    </Page>
</mvc:View>
