<mvc:View controllerName="reconciliation.controller.Actions"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns="sap.m"
    xmlns:f="sap.f"
    xmlns:core="sap.ui.core">
    <Page id="actionsPage" title="{i18n>actionsTitle}" showNavButton="true" navButtonPress="onNavBack">
        <content>
            <ScrollContainer id="actionsScrollContainer" height="100%" width="100%" horizontal="false" vertical="true">
                
                <!-- Actions Overview Header -->
                <VBox id="actionsHeaderSection" class="sapUiMediumMargin">
                    <Title id="actionsHeaderTitle" text="Actions Tracking & Monitoring" level="H2" class="sapUiMediumMarginBottom"/>
                    <MessageStrip id="actionsInfoStrip" 
                        text="Monitor and track the execution status of all reconciliation actions including stock reallocations, procurement requests, and production orders."
                        type="Information"
                        class="sapUiMediumMarginBottom"/>
                </VBox>

                <!-- K<PERSON> Tiles Section -->
                <VBox id="actionsKpiSection" class="sapUiMediumMargin">
                    <Title id="actionsKpiTitle" text="Action Status Overview" level="H3" class="sapUiMediumMarginBottom"/>
                    <f:GridContainer id="actionsKpiGridContainer" class="sapUiResponsiveMargin">
                        <f:layout>
                            <f:GridContainerSettings id="kpiGridContainerSettings" rowSize="5rem" columnSize="5rem" gap="1rem"/>
                        </f:layout>
                        
                        <GenericTile id="totalActionsTile" class="sapUiTinyMargin" press="onKPITilePress">
                            <TileContent id="totalActionsTileContent">
                                <NumericContent id="totalActionsContent" 
                                    value="{actionsModel>/kpis/totalActions}" 
                                    valueColor="Good"
                                    indicator="None"/>
                            </TileContent>
                            <tileContent>
                                <TileContent id="totalActionsTextContent">
                                    <content>
                                        <Text id="totalActionsText" text="Total Actions"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <GenericTile id="pendingActionsTile" class="sapUiTinyMargin" press="onKPITilePress">
                            <TileContent id="pendingActionsTileContent">
                                <NumericContent id="pendingActionsContent" 
                                    value="{actionsModel>/kpis/pendingActions}" 
                                    valueColor="Critical"
                                    indicator="Up"/>
                            </TileContent>
                            <tileContent>
                                <TileContent id="pendingActionsTextContent">
                                    <content>
                                        <Text id="pendingActionsText" text="Pending Actions"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <GenericTile id="inProgressActionsTile" class="sapUiTinyMargin" press="onKPITilePress">
                            <TileContent id="inProgressActionsTileContent">
                                <NumericContent id="inProgressActionsContent" 
                                    value="{actionsModel>/kpis/inProgressActions}" 
                                    valueColor="Error"
                                    indicator="None"/>
                            </TileContent>
                            <tileContent>
                                <TileContent id="inProgressActionsTextContent">
                                    <content>
                                        <Text id="inProgressActionsText" text="In Progress"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <GenericTile id="completedActionsTile" class="sapUiTinyMargin" press="onKPITilePress">
                            <TileContent id="completedActionsTileContent">
                                <NumericContent id="completedActionsContent" 
                                    value="{actionsModel>/kpis/completedActions}" 
                                    valueColor="Good"
                                    indicator="Up"/>
                            </TileContent>
                            <tileContent>
                                <TileContent id="completedActionsTextContent">
                                    <content>
                                        <Text id="completedActionsText" text="Completed"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <GenericTile id="successRateTile" class="sapUiTinyMargin" press="onKPITilePress">
                            <TileContent id="successRateTileContent">
                                <NumericContent id="successRateContent" 
                                    value="{path: 'actionsModel>/kpis/successRate', formatter: '.formatPercentage'}" 
                                    valueColor="Good"
                                    indicator="Up"/>
                            </TileContent>
                            <tileContent>
                                <TileContent id="successRateTextContent">
                                    <content>
                                        <Text id="successRateText" text="Success Rate"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <GenericTile id="avgExecutionTimeTile" class="sapUiTinyMargin" press="onKPITilePress">
                            <TileContent id="avgExecutionTimeTileContent">
                                <NumericContent id="avgExecutionTimeContent" 
                                    value="{path: 'actionsModel>/kpis/avgExecutionTime', formatter: '.formatDuration'}" 
                                    valueColor="Neutral"
                                    indicator="None"/>
                            </TileContent>
                            <tileContent>
                                <TileContent id="avgExecutionTimeTextContent">
                                    <content>
                                        <Text id="avgExecutionTimeText" text="Avg. Execution Time"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>
                    </f:GridContainer>
                </VBox>

                <!-- Filter and Control Section -->
                <VBox id="actionsControlSection" class="sapUiMediumMargin">
                    <Panel id="actionsControlPanel" headerText="Action Filters" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="actionsFilterBar" class="sapUiMediumMargin">
                                <ComboBox id="actionsActionTypeFilter"
                                    placeholder="Filter by Action Type"
                                    selectedKey="{actionsModel>/filters/actionType}"
                                    selectionChange="onFilterChange"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item id="_IDGenItem" key="" text="All Action Types"/>
                                    <core:Item id="_IDGenItem1" key="STOCK_REALLOCATION" text="Stock Reallocation"/>
                                    <core:Item id="_IDGenItem2" key="PROCUREMENT_REQUEST" text="Procurement Request"/>
                                    <core:Item id="_IDGenItem3" key="PRODUCTION_ORDER" text="Production Order"/>
                                </ComboBox>
                                <ComboBox id="actionsStatusFilter"
                                    placeholder="Filter by Status"
                                    selectedKey="{actionsModel>/filters/status}"
                                    selectionChange="onFilterChange"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item id="_IDGenItem4" key="" text="All Status"/>
                                    <core:Item id="_IDGenItem5" key="PENDING" text="Pending"/>
                                    <core:Item id="_IDGenItem6" key="IN_PROGRESS" text="In Progress"/>
                                    <core:Item id="_IDGenItem7" key="COMPLETED" text="Completed"/>
                                    <core:Item id="_IDGenItem8" key="FAILED" text="Failed"/>
                                    <core:Item id="_IDGenItem9" key="CANCELLED" text="Cancelled"/>
                                </ComboBox>
                                <ComboBox id="priorityFilter" 
                                    placeholder="Filter by Priority"
                                    selectedKey="{actionsModel>/filters/priority}"
                                    selectionChange="onFilterChange"
                                    class="sapUiTinyMarginEnd">
                                    <core:Item id="_IDGenItem10" key="" text="All Priorities"/>
                                    <core:Item id="_IDGenItem11" key="HIGH" text="High Priority"/>
                                    <core:Item id="_IDGenItem12" key="MEDIUM" text="Medium Priority"/>
                                    <core:Item id="_IDGenItem13" key="LOW" text="Low Priority"/>
                                </ComboBox>
                                <DateRangeSelection id="dateRangeFilter" 
                                    placeholder="Filter by Date Range"
                                    change="onDateRangeChange"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="actionsClearFiltersButton"
                                    text="{i18n>clearFilters}"
                                    icon="sap-icon://clear-filter"
                                    type="Transparent"
                                    press="onClearFilters"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="refreshActionsButton" 
                                    text="{i18n>refresh}" 
                                    icon="sap-icon://refresh"
                                    type="Default" 
                                    press="onRefreshActions"/>
                            </HBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Quick Actions Section -->
                <VBox id="actionsQuickActionsSection" class="sapUiMediumMargin">
                    <Panel id="actionsQuickActionsPanel" headerText="Quick Actions" class="sapUiResponsiveMargin">
                        <content>
                            <HBox id="actionsQuickActionsBar" class="sapUiMediumMargin">
                                <Button id="createStockReallocationButton" 
                                    text="Create Stock Reallocation" 
                                    icon="sap-icon://inventory"
                                    type="Default" 
                                    press="onCreateStockReallocation"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="createProcurementRequestButton" 
                                    text="Create Procurement Request" 
                                    icon="sap-icon://cart"
                                    type="Default" 
                                    press="onCreateProcurementRequest"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="createProductionOrderButton" 
                                    text="Create Production Order" 
                                    icon="sap-icon://factory"
                                    type="Default" 
                                    press="onCreateProductionOrder"
                                    class="sapUiTinyMarginEnd"/>
                                <Button id="bulkUpdateStatusButton" 
                                    text="Bulk Update Status" 
                                    icon="sap-icon://batch-payments"
                                    type="Default" 
                                    press="onBulkUpdateStatus"
                                    enabled="{actionsModel>/hasSelectedActions}"/>
                            </HBox>
                        </content>
                    </Panel>
                </VBox>

                <!-- Actions Table -->
                <VBox id="actionsTableSection" class="sapUiMediumMargin">
                    <Table id="actionsTable"
                        items="{actionsModel>/actions}"
                        mode="MultiSelect"
                        selectionChange="onSelectionChange"
                        class="sapUiResponsiveMargin"
                        growing="true"
                        growingThreshold="50">
                        <headerToolbar>
                            <Toolbar id="actionsToolbar">
                                <Title id="actionsTableTitle" text="Actions ({actionsModel>/actions/length})"/>
                                <ToolbarSpacer id="actionsToolbarSpacer"/>
                                <Button id="exportActionsButton" 
                                    text="{i18n>export}" 
                                    icon="sap-icon://excel-attachment"
                                    type="Transparent" 
                                    press="onExportActions"/>
                            </Toolbar>
                        </headerToolbar>
                        <columns>
                            <Column id="actionIdColumn">
                                <Text id="actionIdColumnHeader" text="Action ID"/>
                            </Column>
                            <Column id="actionsActionTypeColumn">
                                <Text id="actionsActionTypeColumnHeader" text="Type"/>
                            </Column>
                            <Column id="actionsPlantColumn">
                                <Text id="actionsPlantColumnHeader" text="Plant"/>
                            </Column>
                            <Column id="actionsProductColumn">
                                <Text id="actionsProductColumnHeader" text="Product"/>
                            </Column>
                            <Column id="actionsDescriptionColumn">
                                <Text id="actionsDescriptionColumnHeader" text="Description"/>
                            </Column>
                            <Column id="actionsPriorityColumn">
                                <Text id="actionsPriorityColumnHeader" text="Priority"/>
                            </Column>
                            <Column id="actionsStatusColumn">
                                <Text id="actionsStatusColumnHeader" text="Status"/>
                            </Column>
                            <Column id="actionsProgressColumn">
                                <Text id="actionsProgressColumnHeader" text="Progress"/>
                            </Column>
                            <Column id="actionsCreatedAtColumn">
                                <Text id="actionsCreatedAtColumnHeader" text="Created"/>
                            </Column>
                            <Column id="actionsActionsColumn">
                                <Text id="actionsActionsColumnHeader" text="Actions"/>
                            </Column>
                        </columns>
                        <items>
                            <ColumnListItem id="actionListItem" press="onActionItemPress">
                                <Link id="actionIdLink" text="{actionsModel>actionId}" press="onActionItemPress"/>
                                <ObjectStatus id="actionsActionTypeStatus"
                                    text="{path: 'actionsModel>actionType', formatter: '.formatActionType'}"
                                    state="{path: 'actionsModel>actionType', formatter: '.formatActionTypeState'}"/>
                                <Text id="actionsPlantText" text="{actionsModel>plant/plantName}"/>
                                <Text id="actionsProductText" text="{actionsModel>product/productName}"/>
                                <Text id="actionsDescriptionText" text="{actionsModel>description}" maxLines="2"/>
                                <ObjectStatus id="actionsPriorityStatus"
                                    text="{actionsModel>priority}"
                                    state="{path: 'actionsModel>priority', formatter: '.formatPriorityState'}"/>
                                <ObjectStatus id="actionsStatusObject"
                                    text="{actionsModel>status}"
                                    state="{path: 'actionsModel>status', formatter: '.formatStatusState'}"/>
                                <ProgressIndicator id="actionsProgressIndicator"
                                    percentValue="{actionsModel>progress}"
                                    displayValue="{path: 'actionsModel>progress', formatter: '.formatProgressDisplay'}"
                                    state="{path: 'actionsModel>progress', formatter: '.formatProgressState'}"
                                    width="100px"/>
                                <Text id="actionsCreatedAtText" text="{path: 'actionsModel>createdAt', formatter: '.formatDate'}"/>
                                <HBox id="actionsActionButtons">
                                    <Button id="actionsViewDetailsButton"
                                        icon="sap-icon://detail-view" 
                                        type="Transparent" 
                                        press="onViewActionDetails"
                                        tooltip="View Details"/>
                                    <Button id="updateStatusButton" 
                                        icon="sap-icon://edit" 
                                        type="Transparent" 
                                        press="onUpdateActionStatus"
                                        tooltip="Update Status"/>
                                    <Button id="cancelActionButton" 
                                        icon="sap-icon://cancel" 
                                        type="Transparent" 
                                        press="onCancelAction"
                                        tooltip="Cancel Action"
                                        visible="{= ${actionsModel>status} === 'PENDING' || ${actionsModel>status} === 'IN_PROGRESS'}"/>
                                </HBox>
                            </ColumnListItem>
                        </items>
                    </Table>
                </VBox>
            </ScrollContainer>
        </content>
    </Page>
</mvc:View>
