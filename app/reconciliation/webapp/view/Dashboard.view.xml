<mvc:View controllerName="reconciliation.controller.Dashboard"
    xmlns:mvc="sap.ui.core.mvc"
    xmlns="sap.m"
    xmlns:f="sap.f"
    xmlns:suite="sap.suite.ui.commons">
    <Page id="page" title="{i18n>dashboardTitle}" showNavButton="true" navButtonPress="onNavBack">
        <content>
            <ScrollContainer id="dashboardScrollContainer" height="100%" width="100%" horizontal="false" vertical="true">
                <!-- KPI Tiles Section -->
                <VBox id="dashboardKpiSection" class="sapUiMediumMargin">
                    <Title id="dashboardKpiTitle" text="Key Performance Indicators" level="H2" class="sapUiMediumMarginBottom"/>
                    <f:GridContainer id="dashboardKpiGridContainer" class="sapUiResponsiveMargin">
                        <!-- Total Variance KPI -->
                        <GenericTile id="varianceTile"
                            header="{i18n>kpiTotalVariance}"
                            subheader="Current Period"
                            press="onKPITilePress"
                            class="sapUiTinyMargin">
                            <tileContent>
                                <TileContent id="varianceTileContent">
                                    <content>
                                        <NumericContent id="varianceNumericContent"
                                            value="{dashboardModel>/kpis/totalVariance}"
                                            valueColor="{path: 'dashboardModel>/kpis/totalVariance', formatter: '.formatVarianceColor'}"
                                            indicator="{path: 'dashboardModel>/kpis/varianceTrend', formatter: '.formatTrendIndicator'}"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <!-- Shortage Count KPI -->
                        <GenericTile id="shortageTile"
                            header="{i18n>kpiShortageCount}"
                            subheader="Active Shortages"
                            press="onKPITilePress"
                            class="sapUiTinyMargin">
                            <tileContent>
                                <TileContent id="shortageTileContent">
                                    <content>
                                        <NumericContent id="shortageNumericContent"
                                            value="{dashboardModel>/kpis/shortageCount}"
                                            valueColor="Error"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <!-- Surplus Count KPI -->
                        <GenericTile id="surplusTile"
                            header="{i18n>kpiSurplusCount}"
                            subheader="Surplus Items"
                            press="onKPITilePress"
                            class="sapUiTinyMargin">
                            <tileContent>
                                <TileContent id="surplusTileContent">
                                    <content>
                                        <NumericContent id="surplusNumericContent"
                                            value="{dashboardModel>/kpis/surplusCount}"
                                            valueColor="Good"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <!-- Pending Approvals KPI -->
                        <GenericTile id="approvalsTile"
                            header="{i18n>kpiPendingApprovals}"
                            subheader="Awaiting Review"
                            press="onNavigateToRecommendations"
                            class="sapUiTinyMargin">
                            <tileContent>
                                <TileContent id="approvalsTileContent">
                                    <content>
                                        <NumericContent id="approvalsNumericContent"
                                            value="{dashboardModel>/kpis/pendingApprovals}"
                                            valueColor="Critical"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <!-- Auto-Triggered Actions KPI -->
                        <GenericTile id="autoTriggerTile"
                            header="{i18n>kpiAutoTriggered}"
                            subheader="This Week"
                            press="onNavigateToActions"
                            class="sapUiTinyMargin">
                            <tileContent>
                                <TileContent id="autoTriggerTileContent">
                                    <content>
                                        <NumericContent id="autoTriggerNumericContent"
                                            value="{dashboardModel>/kpis/autoTriggeredActions}"
                                            valueColor="Good"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>

                        <!-- Average AI Confidence KPI -->
                        <GenericTile id="confidenceTile"
                            header="{i18n>kpiAvgConfidence}"
                            subheader="AI Recommendations"
                            press="onKPITilePress"
                            class="sapUiTinyMargin">
                            <tileContent>
                                <TileContent id="confidenceTileContent">
                                    <content>
                                        <NumericContent id="confidenceNumericContent"
                                            value="{path: 'dashboardModel>/kpis/avgConfidenceScore', formatter: '.formatPercentage'}"
                                            valueColor="{path: 'dashboardModel>/kpis/avgConfidenceScore', formatter: '.formatConfidenceColor'}"/>
                                    </content>
                                </TileContent>
                            </tileContent>
                        </GenericTile>
                    </f:GridContainer>
                </VBox>

                <!-- Quick Actions Section -->
                <VBox id="quickActionsSection" class="sapUiMediumMargin">
                    <Title id="quickActionsTitle" text="Quick Actions" level="H2" class="sapUiMediumMarginBottom"/>
                    <HBox id="quickActionsBox" class="sapUiResponsiveMargin">
                        <Button id="uploadButton" text="{i18n>navUpload}"
                            icon="sap-icon://upload"
                            type="Emphasized"
                            press="onNavigateToUpload"
                            class="sapUiTinyMarginEnd"/>
                        <Button id="reconciliationButton" text="{i18n>navReconciliation}"
                            icon="sap-icon://process"
                            type="Default"
                            press="onNavigateToReconciliation"
                            class="sapUiTinyMarginEnd"/>
                        <Button id="recommendationsButton" text="{i18n>navRecommendations}"
                            icon="sap-icon://ai"
                            type="Default"
                            press="onNavigateToRecommendations"
                            class="sapUiTinyMarginEnd"/>
                        <Button id="refreshButton" text="{i18n>refresh}"
                            icon="sap-icon://refresh"
                            type="Transparent"
                            press="onRefreshDashboard"/>
                    </HBox>
                </VBox>

                <!-- Recent Activity Section -->
                <VBox id="recentActivitySection" class="sapUiMediumMargin">
                    <Title id="recentActivityTitle" text="{i18n>recentReconciliations}" level="H2" class="sapUiMediumMarginBottom"/>
                    <Table id="recentReconciliationsTable"
                        items="{dashboardModel>/recentReconciliations}"
                        mode="None"
                        class="sapUiResponsiveMargin">
                        <columns>
                            <Column id="dashboardDateColumn"><Text id="dashboardDateColumnText" text="Date"/></Column>
                            <Column id="dashboardPlantColumn"><Text id="dashboardPlantColumnText" text="Plant"/></Column>
                            <Column id="dashboardProductColumn"><Text id="dashboardProductColumnText" text="Product"/></Column>
                            <Column id="dashboardVarianceColumn"><Text id="dashboardVarianceColumnText" text="Variance"/></Column>
                            <Column id="dashboardStatusColumn"><Text id="dashboardStatusColumnText" text="Status"/></Column>
                            <Column id="dashboardActionsColumn"><Text id="dashboardActionsColumnText" text="Actions"/></Column>
                        </columns>
                        <items>
                            <ColumnListItem id="reconciliationListItem" press="onReconciliationItemPress">
                                <Text id="dashboardDateText" text="{dashboardModel>reconciliationDate}"/>
                                <Text id="dashboardPlantText" text="{dashboardModel>plant/plantName}"/>
                                <Text id="dashboardProductText" text="{dashboardModel>product/productName}"/>
                                <ObjectNumber id="dashboardVarianceNumber" number="{dashboardModel>variance}"
                                    unit="{dashboardModel>product/unitOfMeasure}"
                                    state="{path: 'dashboardModel>variance', formatter: '.formatVarianceState'}"/>
                                <ObjectStatus id="dashboardStatusObject" text="{dashboardModel>status}"
                                    state="{path: 'dashboardModel>status', formatter: '.formatStatusState'}"/>
                                <Button id="dashboardDetailsButton" icon="sap-icon://detail-view"
                                    type="Transparent"
                                    press="onViewReconciliationDetails"/>
                            </ColumnListItem>
                        </items>
                    </Table>
                </VBox>
            </ScrollContainer>
        </content>
    </Page>
</mvc:View>